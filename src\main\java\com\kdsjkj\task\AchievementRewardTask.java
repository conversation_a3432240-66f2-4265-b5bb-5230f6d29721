package com.kdsjkj.task;

import com.kdsjkj.entity.AchievementRewards;
import com.kdsjkj.entity.Agent;
import com.kdsjkj.service.IAchievementRewardsService;
import com.kdsjkj.service.IAgentService;
import com.kdsjkj.service.IServiceMarketOrderService;
import com.kdsjkj.service.IOrderService;
import com.kdsjkj.service.IAgentFundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 达标奖励计算定时任务
 */
@Slf4j
@Component
public class AchievementRewardTask {

    @Autowired
    private IAchievementRewardsService achievementRewardsService;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private IServiceMarketOrderService serviceMarketOrderService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IAgentFundService agentFundService;

    /**
     * 每月1号凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 1 * ?")
    @Transactional(rollbackFor = Exception.class)
    public void calculateAchievementRewards() {
        log.info("开始执行达标奖励计算任务");

        try {
            // 1. 获取上个月的时间范围
            YearMonth lastMonth = YearMonth.now().minusMonths(1);
            LocalDateTime startTime = lastMonth.atDay(1).atStartOfDay();
            LocalDateTime endTime = lastMonth.atEndOfMonth().atTime(23, 59, 59);
            log.info("计算时间范围：{} 至 {}", startTime, endTime);

            // 2. 获取达标奖励规则
            AchievementRewards rewardRule = achievementRewardsService.getAchievementReward();
            if (rewardRule == null) {
                log.error("未找到达标奖励规则配置");
                return;
            }
            log.info("获取到达标奖励规则：最低商户数={}, 每月最低收入={}, 奖励金额={}", 
                    rewardRule.getMinMerchantCount(), 
                    rewardRule.getMinMonthlyIncome(),
                    rewardRule.getRewardAmount());

            // 3. 获取所有代理
            List<Agent> allAgents = agentService.list();
            log.info("获取到代理总数：{}", allAgents.size());

            // 遍历每个代理进行达标检查
            for (Agent agent : allAgents) {
                try {
                    processAgentReward(agent, startTime, endTime, rewardRule);
                } catch (Exception e) {
                    log.error("处理代理{}达标奖励时发生错误", agent.getUid(), e);
                }
            }

            log.info("达标奖励计算任务执行完成");
        } catch (Exception e) {
            log.error("达标奖励计算任务执行失败", e);
            throw e;
        }
    }

    /**
     * 处理单个代理的达标奖励
     */
    private void processAgentReward(Agent agent, LocalDateTime startTime, LocalDateTime endTime, AchievementRewards rewardRule) {
        String agentUid = agent.getUid();
        log.info("开始处理代理{}的达标奖励", agentUid);

        // 3.1 获取代理的商户数量和appid列表
        List<String> merchantAppIds = serviceMarketOrderService.getMerchantAppIdsByAgentUidAndTimeRange(
                agentUid, startTime, endTime);
        
        int merchantCount = merchantAppIds.size();
        log.info("代理{}在{}至{}期间邀请的商户数：{}", agentUid, startTime, endTime, merchantCount);

        // 如果商户数量不满足最低要求，直接返回
        if (merchantCount < rewardRule.getMinMerchantCount()) {
            log.info("代理{}商户数{}未达到最低要求{}，不发放奖励", agentUid, merchantCount, rewardRule.getMinMerchantCount());
            return;
        }

        // 3.2 计算每个商户的交易金额
        int qualifiedMerchantCount = 0;
        for (String appId : merchantAppIds) {
            BigDecimal monthlyIncome = calculateMerchantMonthlyIncome(appId, startTime, endTime);
            if (monthlyIncome.compareTo(rewardRule.getMinMonthlyIncome()) >= 0) {
                qualifiedMerchantCount++;
            }
            log.debug("商户{}月收入：{}", appId, monthlyIncome);
        }

        // 4. 判断是否满足奖励条件并发放奖励
        if (qualifiedMerchantCount >= rewardRule.getMinMerchantCount()) {
            log.info("代理{}满足达标奖励条件，达标商户数：{}", agentUid, qualifiedMerchantCount);
            // 发放奖励
            boolean success = agentFundService.saveAgentFund(agentUid, rewardRule.getRewardAmount(), 1);
            if (success) {
                log.info("代理{}达标奖励发放成功，奖励金额：{}", agentUid, rewardRule.getRewardAmount());
            } else {
                log.error("代理{}达标奖励发放失败", agentUid);
                throw new RuntimeException("奖励发放失败");
            }
        } else {
            log.info("代理{}达标商户数{}未达到要求，不发放奖励", agentUid, qualifiedMerchantCount);
        }
    }

    /**
     * 计算商户月收入
     */
    private BigDecimal calculateMerchantMonthlyIncome(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        // 查询并汇总商户在指定时间范围内的订单金额
        List<BigDecimal> orderAmounts = orderService.lambdaQuery()
                .eq(Order::getAppId, appId)
                .between(Order::getCreateTime, startTime, endTime)
                .eq(Order::getTradeStatus, "TRADE_SUCCESS")  // 只统计成功的订单
                .list()
                .stream()
                .map(Order::getAmount)
                .collect(Collectors.toList());

        return orderAmounts.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
} 